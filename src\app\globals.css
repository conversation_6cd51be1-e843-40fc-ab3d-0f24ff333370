@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #111827;
  --primary: #07243C;
  --secondary: #6b7280;
  --accent: #F3CC5C;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  line-height: 1.6;
  font-weight: 400;
  letter-spacing: -0.01em;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
  scroll-padding-top: 64px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Selection styling */
::selection {
  background: rgba(17, 24, 39, 0.1);
  color: #111827;
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid #07243C;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Typography enhancements */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 300;
  letter-spacing: -0.02em;
}

/* Smooth transitions for all interactive elements */
a,
button,
input,
textarea {
  transition: all 0.2s ease-in-out;
}
