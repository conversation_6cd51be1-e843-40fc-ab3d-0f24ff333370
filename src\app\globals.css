@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #111827;
  --primary: #7c3aed;
  --primary-dark: #6d28d9;
  --secondary: #3b82f6;
  --accent: #ec4899;
  --gradient: linear-gradient(135deg, #7c3aed 0%, #3b82f6 100%);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-dark: var(--primary-dark);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.7;
  font-weight: 300;
  letter-spacing: -0.01em;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #7c3aed, #3b82f6);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #6d28d9, #2563eb);
}

/* Enhanced animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(124, 58, 237, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(124, 58, 237, 0.6);
  }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Gradient text animation */
.gradient-text {
  background: linear-gradient(135deg, #7c3aed, #3b82f6, #ec4899);
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Enhanced button styles */
.btn-gradient {
  background: linear-gradient(135deg, #7c3aed, #3b82f6);
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(124, 58, 237, 0.4);
}

/* Floating elements */
.float {
  animation: float 6s ease-in-out infinite;
}

/* Selection styling */
::selection {
  background: rgba(124, 58, 237, 0.2);
  color: #7c3aed;
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid #7c3aed;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Typography enhancements */
h1, h2, h3, h4, h5, h6 {
  font-weight: 200;
  letter-spacing: -0.02em;
}

/* Smooth transitions for all interactive elements */
a, button, input, textarea {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced card hover effects */
.card-hover {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
}
