"use client";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";

export default function Home() {
  const [currentSet, setCurrentSet] = useState(0);

  // Gallery images with proper naming and descriptions
  const galleryImages = [
    { src: "/images/gallery/student-graduation.jpg", title: "Graduation Ceremony", description: "Celebrating academic achievements" },
    { src: "/images/gallery/classroom-learning.jpg", title: "Interactive Learning", description: "Students engaged in collaborative study" },
    { src: "/images/gallery/science-laboratory.jpg", title: "Science Laboratory", description: "Hands-on scientific exploration" },
    { src: "/images/gallery/library-study.jpg", title: "Library Resources", description: "Quiet spaces for focused learning" },
    { src: "/images/gallery/sports-activities.jpg", title: "Athletic Programs", description: "Building teamwork and fitness" },
    { src: "/images/gallery/art-creativity.jpg", title: "Creative Arts", description: "Expressing imagination through art" },
    { src: "/images/gallery/technology-lab.jpg", title: "Technology Center", description: "Modern digital learning tools" },
    { src: "/images/gallery/music-performance.jpg", title: "Musical Excellence", description: "Developing artistic talents" },
    { src: "/images/gallery/outdoor-activities.jpg", title: "Outdoor Learning", description: "Education beyond the classroom" },
    { src: "/images/gallery/student-presentation.jpg", title: "Student Presentations", description: "Building confidence and communication" },
    { src: "/images/gallery/group-discussion.jpg", title: "Group Discussions", description: "Collaborative problem solving" },
    { src: "/images/gallery/awards-ceremony.jpg", title: "Recognition Events", description: "Honoring student achievements" },
    { src: "/images/gallery/campus-life.jpg", title: "Campus Community", description: "Vibrant student life experiences" },
    { src: "/images/gallery/teacher-mentoring.jpg", title: "Expert Mentoring", description: "Dedicated faculty guidance" },
    { src: "/images/gallery/cultural-events.jpg", title: "Cultural Celebrations", description: "Embracing diversity and traditions" },
    { src: "/images/gallery/innovation-projects.jpg", title: "Innovation Hub", description: "Student-led research projects" },
    { src: "/images/gallery/leadership-training.jpg", title: "Leadership Development", description: "Preparing future leaders" },
    { src: "/images/gallery/community-service.jpg", title: "Community Service", description: "Making a positive impact" },
    { src: "/images/gallery/academic-excellence.jpg", title: "Academic Success", description: "Pursuing educational excellence" },
    { src: "/images/gallery/global-connections.jpg", title: "Global Perspectives", description: "International learning opportunities" },
    { src: "/images/gallery/future-ready.jpg", title: "Future Preparation", description: "Preparing for tomorrow's challenges" }
  ];

  const galleryImageSets = [
    {
      layout: "masonry", // Large featured + 6 smaller
      images: galleryImages.slice(0, 7)
    },
    {
      layout: "grid", // Equal sized grid
      images: galleryImages.slice(7, 14)
    },
    {
      layout: "vertical", // Three-column layout
      images: galleryImages.slice(14, 21)
    }
  ];

  // Auto-scroll functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSet((prev) => (prev + 1) % galleryImageSets.length);
    }, 5000); // Change every 5 seconds

    return () => clearInterval(interval);
  }, [galleryImageSets.length]);

  const currentSet_data = galleryImageSets[currentSet];
  const currentImages = currentSet_data.images;
  const currentLayout = currentSet_data.layout;

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/95 backdrop-blur-sm z-50 border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <Image
                src="/images/wlc-logo.png"
                alt="WLC Academy"
                width={32}
                height={32}
                className="rounded-lg"
              />
              <span className="text-xl font-medium text-gray-900">WLC Academy</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <Link href="#home" className="text-gray-700 hover:text-gray-900 text-sm font-medium transition-colors duration-200">
                Home
              </Link>
              <Link href="#about" className="text-gray-700 hover:text-gray-900 text-sm font-medium transition-colors duration-200">
                About
              </Link>
              <Link href="#programs" className="text-gray-700 hover:text-gray-900 text-sm font-medium transition-colors duration-200">
                Programs
              </Link>
              <Link href="#contact" className="text-gray-700 hover:text-gray-900 text-sm font-medium transition-colors duration-200">
                Contact
              </Link>
              <button className="bg-[#07243C] text-white px-6 py-2 rounded-lg text-sm font-medium hover:bg-[#0a2d47] transition-colors duration-200">
                Sign Up
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="relative min-h-screen flex items-center justify-center bg-white">
        {/* Content */}
        <div className="relative z-10 max-w-5xl mx-auto px-6 text-center">
          <div className="mb-12">
            <div className="inline-flex items-center px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-8">
              🎓 Excellence in Education
            </div>
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-light text-gray-900 mb-6 leading-tight tracking-tight">
              Shaping Tomorrow's
              <br />
              <span className="font-medium">Leaders</span>
            </h1>
            <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto mb-12">
              Empowering students to reach their full potential through innovative learning,
              dedicated teaching, and a commitment to academic excellence.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16">
            <button className="bg-[#07243C] text-white px-8 py-3 rounded-lg font-medium hover:bg-[#0a2d47] transition-colors duration-200">
              Start Your Journey
            </button>
            <button className="text-[#07243C] hover:text-[#0a2d47] px-8 py-3 font-medium transition-colors duration-200 flex items-center border border-[#07243C] hover:border-[#0a2d47] rounded-lg">
              Learn More
              <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-3xl mx-auto pt-8 border-t border-gray-100">
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-medium text-gray-900 mb-1">500+</div>
              <div className="text-sm text-gray-600">Students</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-medium text-gray-900 mb-1">50+</div>
              <div className="text-sm text-gray-600">Expert Teachers</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-medium text-gray-900 mb-1">15+</div>
              <div className="text-sm text-gray-600">Years Experience</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-medium text-gray-900 mb-1">98%</div>
              <div className="text-sm text-gray-600">Success Rate</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
              Why Choose WLC Academy
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Three pillars that define our commitment to excellence
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center bg-white p-8 rounded-xl border border-gray-100 hover:border-[#F3CC5C] transition-colors duration-200">
              <div className="w-16 h-16 bg-[#F3CC5C] rounded-xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-[#07243C]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-xl font-medium text-[#07243C] mb-3">Academic Excellence</h3>
              <p className="text-gray-600 leading-relaxed">Innovative curriculum designed to challenge minds and inspire breakthrough thinking</p>
            </div>

            <div className="text-center bg-white p-8 rounded-xl border border-gray-100 hover:border-[#F3CC5C] transition-colors duration-200">
              <div className="w-16 h-16 bg-[#07243C] rounded-xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-[#F3CC5C]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-medium text-[#07243C] mb-3">Expert Mentors</h3>
              <p className="text-gray-600 leading-relaxed">World-class educators dedicated to nurturing potential and fostering growth</p>
            </div>

            <div className="text-center bg-white p-8 rounded-xl border border-gray-100 hover:border-[#F3CC5C] transition-colors duration-200">
              <div className="w-16 h-16 bg-[#F3CC5C] rounded-xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-[#07243C]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-medium text-[#07243C] mb-3">Future Ready</h3>
              <p className="text-gray-600 leading-relaxed">Cutting-edge facilities and technology preparing students for tomorrow's challenges</p>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-24 bg-white">
        <div className="max-w-6xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <div className="mb-8">
                <div className="inline-block px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-6">
                  About WLC Academy
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-6 leading-tight">
                  Shaping minds for a brighter tomorrow
                </h2>
              </div>

              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                Founded with a vision to redefine education, WLC Academy stands at the intersection
                of innovation and excellence. We don't just teach—we inspire, challenge, and
                transform lives.
              </p>

              <p className="text-gray-600 leading-relaxed mb-8">
                Our approach goes beyond traditional learning, fostering critical thinking,
                creativity, and leadership skills that prepare students for an ever-evolving world.
              </p>

              <button className="bg-[#07243C] text-white px-8 py-3 rounded-lg font-medium hover:bg-[#0a2d47] transition-colors duration-200">
                Discover Our Story
              </button>
            </div>

            <div className="relative">
              <div className="bg-gray-50 rounded-2xl p-8">
                <div className="grid grid-cols-1 gap-8">
                  <div className="text-center">
                    <h3 className="text-xl font-medium text-gray-900 mb-2">Our Mission</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      Empowering students to become confident, capable leaders who shape the future
                    </p>
                  </div>
                  <div className="text-center">
                    <h3 className="text-xl font-medium text-gray-900 mb-2">Our Vision</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      Setting the global standard for educational excellence and innovation
                    </p>
                  </div>
                </div>

                <div className="mt-8 pt-8 border-t border-gray-200">
                  <div className="grid grid-cols-2 gap-8 text-center">
                    <div>
                      <div className="text-2xl font-medium text-gray-900 mb-1">2014</div>
                      <div className="text-sm text-gray-600">Founded</div>
                    </div>
                    <div>
                      <div className="text-2xl font-medium text-gray-900 mb-1">Global</div>
                      <div className="text-sm text-gray-600">Recognition</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center mb-16">
            <div className="inline-block px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-6">
              Gallery
            </div>
            <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
              Moments of Excellence
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Capturing the spirit of learning, growth, and achievement
            </p>
          </div>

          {/* Dynamic Image Grid with Auto-Scroll - Same Area, Different Arrangements */}
          <div className="h-[600px] transition-all duration-1000 ease-in-out">
            {/* Layout 1: Large Left + Small Right Grid */}
            {currentLayout === "masonry" && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 h-full">
                {/* Large Featured Image - Left Side */}
                <div className="md:col-span-2">
                  <div className="relative h-full bg-gray-200 rounded-xl overflow-hidden group">
                    <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="absolute bottom-4 left-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <h3 className="text-lg font-medium">{currentImages[0].title}</h3>
                      <p className="text-sm text-gray-200">{currentImages[0].description}</p>
                    </div>
                    <img
                      src={currentImages[0].src}
                      alt={currentImages[0].title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
                {/* Medium Images - Center */}
                <div className="grid grid-rows-2 gap-6">
                  {currentImages.slice(1, 3).map((image: any, index: number) => (
                    <div key={index + 1} className="relative bg-gray-200 rounded-lg overflow-hidden group">
                      <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="absolute bottom-2 left-2 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <h4 className="text-sm font-medium">{image.title}</h4>
                      </div>
                      <img
                        src={image.src}
                        alt={image.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
                {/* Small Images Grid - Right Side */}
                <div className="grid grid-rows-4 gap-3">
                  {currentImages.slice(3).map((image: any, index: number) => (
                    <div key={index + 3} className="relative bg-gray-200 rounded-lg overflow-hidden group">
                      <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="absolute bottom-1 left-2 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <h4 className="text-xs font-medium">{image.title}</h4>
                      </div>
                      <img
                        src={image.src}
                        alt={image.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Layout 2: Grid Pattern with Varied Sizes */}
            {currentLayout === "grid" && (
              <div className="grid grid-cols-4 grid-rows-4 gap-4 h-full">
                {/* Large Top Left */}
                <div className="col-span-2 row-span-2">
                  <div className="relative h-full bg-gray-200 rounded-xl overflow-hidden group">
                    <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="absolute bottom-4 left-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <h3 className="text-lg font-medium">{currentImages[0].title}</h3>
                      <p className="text-sm text-gray-200">{currentImages[0].description}</p>
                    </div>
                    <img
                      src={currentImages[0].src}
                      alt={currentImages[0].title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
                {/* Top Right Column */}
                <div className="col-span-2 grid grid-rows-2 gap-4">
                  {currentImages.slice(1, 3).map((image: any, index: number) => (
                    <div key={index + 1} className="relative bg-gray-200 rounded-lg overflow-hidden group">
                      <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="absolute bottom-2 left-2 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <h4 className="text-sm font-medium">{image.title}</h4>
                      </div>
                      <img
                        src={image.src}
                        alt={image.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
                {/* Bottom Row */}
                <div className="col-span-4 grid grid-cols-4 gap-4">
                  {currentImages.slice(3).map((image: any, index: number) => (
                    <div key={index + 3} className="relative bg-gray-200 rounded-lg overflow-hidden group">
                      <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="absolute bottom-1 left-2 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <h4 className="text-xs font-medium">{image.title}</h4>
                      </div>
                      <img
                        src={image.src}
                        alt={image.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Layout 3: Mixed Grid - No Empty Spaces */}
            {currentLayout === "vertical" && (
              <div className="grid grid-cols-3 gap-6 h-full">
                {/* Left Column - Two stacked images */}
                <div className="grid grid-rows-2 gap-6">
                  <div className="relative bg-gray-200 rounded-xl overflow-hidden group">
                    <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="absolute bottom-3 left-3 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <h4 className="text-sm font-medium">{currentImages[1].title}</h4>
                    </div>
                    <img
                      src={currentImages[2].src}
                      alt={currentImages[2].title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="relative bg-gray-200 rounded-xl overflow-hidden group">
                    <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="absolute bottom-3 left-3 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <h4 className="text-sm font-medium">{currentImages[2].title}</h4>
                    </div>
                    <img
                      src={currentImages[1].src}
                      alt={currentImages[1].title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>

                {/* Center Column - Large featured image */}
                <div className="relative bg-gray-200 rounded-xl overflow-hidden group">
                  <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute bottom-4 left-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <h3 className="text-lg font-medium">{currentImages[0].title}</h3>
                    <p className="text-sm text-gray-200">{currentImages[0].description}</p>
                  </div>
                  <img
                    src={currentImages[0].src}
                    alt={currentImages[0].title}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Right Column - Four stacked images */}
                <div className="grid grid-rows-4 gap-3">
                  {currentImages.slice(3).map((image: any, index: number) => (
                    <div key={index + 3} className="relative bg-gray-200 rounded-lg overflow-hidden group">
                      <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="absolute bottom-1 left-2 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <h4 className="text-xs font-medium">{image.title}</h4>
                      </div>
                      <img
                        src={image.src}
                        alt={image.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Auto-Scroll Indicators */}
          <div className="flex justify-center items-center mt-12 space-x-4">
            <div className="flex space-x-2">
              {galleryImageSets.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentSet(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentSet
                      ? 'bg-[#F3CC5C] scale-110'
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Gallery Info */}
          <div className="text-center mt-8">
            <p className="text-gray-600">
              Showcasing moments of learning, achievement, and community
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Gallery automatically updates every 5 seconds
            </p>
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <section id="programs" className="py-24 bg-gray-50">
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center mb-16">
            <div className="inline-block px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-6">
              Our Programs
            </div>
            <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
              Pathways to Excellence
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Comprehensive programs designed to nurture potential at every stage of learning
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl p-8 border border-gray-100 hover:border-[#F3CC5C] transition-colors duration-200">
              <div className="w-16 h-16 bg-[#F3CC5C] rounded-xl flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-[#07243C]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-xl font-medium text-[#07243C] mb-3">Foundation Years</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Building strong foundations through interactive learning and creative exploration
              </p>
              <div className="space-y-2">
                <div className="flex items-center text-sm text-gray-500">
                  <div className="w-1.5 h-1.5 bg-[#F3CC5C] rounded-full mr-3"></div>
                  Interactive methodologies
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <div className="w-1.5 h-1.5 bg-[#F3CC5C] rounded-full mr-3"></div>
                  Personalized attention
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <div className="w-1.5 h-1.5 bg-[#F3CC5C] rounded-full mr-3"></div>
                  Creative development
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-8 border border-gray-100 hover:border-[#F3CC5C] transition-colors duration-200">
              <div className="w-16 h-16 bg-[#07243C] rounded-xl flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-[#F3CC5C]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-xl font-medium text-[#07243C] mb-3">Advanced Studies</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Rigorous curriculum preparing students for higher education and global challenges
              </p>
              <div className="space-y-2">
                <div className="flex items-center text-sm text-gray-500">
                  <div className="w-1.5 h-1.5 bg-[#F3CC5C] rounded-full mr-3"></div>
                  STEM excellence
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <div className="w-1.5 h-1.5 bg-[#F3CC5C] rounded-full mr-3"></div>
                  Research opportunities
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <div className="w-1.5 h-1.5 bg-[#F3CC5C] rounded-full mr-3"></div>
                  Global perspectives
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-8 border border-gray-100 hover:border-[#F3CC5C] transition-colors duration-200">
              <div className="w-16 h-16 bg-[#F3CC5C] rounded-xl flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-[#07243C]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-medium text-[#07243C] mb-3">Leadership Track</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Specialized programs for developing tomorrow's leaders and innovators
              </p>
              <div className="space-y-2">
                <div className="flex items-center text-sm text-gray-500">
                  <div className="w-1.5 h-1.5 bg-[#F3CC5C] rounded-full mr-3"></div>
                  Leadership development
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <div className="w-1.5 h-1.5 bg-[#F3CC5C] rounded-full mr-3"></div>
                  Innovation labs
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <div className="w-1.5 h-1.5 bg-[#F3CC5C] rounded-full mr-3"></div>
                  Mentorship programs
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-24 bg-white">
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center mb-16">
            <div className="inline-block px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-6">
              Get in Touch
            </div>
            <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
              Ready to begin your journey?
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Take the first step towards excellence. We're here to guide you every step of the way.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <div className="space-y-8">
              <div>
                <h3 className="text-xl font-medium text-gray-900 mb-6">Connect With Us</h3>
                <div className="space-y-6">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-[#F3CC5C] rounded-xl flex items-center justify-center mr-4">
                      <svg className="w-5 h-5 text-[#07243C]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-[#07243C] font-medium">Visit Us</div>
                      <div className="text-gray-600">123 Education Street, Learning City</div>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-[#07243C] rounded-xl flex items-center justify-center mr-4">
                      <svg className="w-5 h-5 text-[#F3CC5C]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-[#07243C] font-medium">Call Us</div>
                      <div className="text-gray-600">(555) 123-4567</div>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-[#F3CC5C] rounded-xl flex items-center justify-center mr-4">
                      <svg className="w-5 h-5 text-[#07243C]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-[#07243C] font-medium">Email Us</div>
                      <div className="text-gray-600"><EMAIL></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-xl p-8">
              <form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <input
                      type="text"
                      placeholder="First Name"
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#F3CC5C] focus:border-transparent text-gray-900 transition-colors duration-200"
                    />
                  </div>
                  <div>
                    <input
                      type="text"
                      placeholder="Last Name"
                      className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#F3CC5C] focus:border-transparent text-gray-900 transition-colors duration-200"
                    />
                  </div>
                </div>
                <div>
                  <input
                    type="email"
                    placeholder="Email Address"
                    className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#F3CC5C] focus:border-transparent text-gray-900 transition-colors duration-200"
                  />
                </div>
                <div>
                  <textarea
                    rows={5}
                    placeholder="Tell us about your interest in WLC Academy..."
                    className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#F3CC5C] focus:border-transparent text-gray-900 transition-colors duration-200 resize-none"
                  ></textarea>
                </div>
                <button
                  type="submit"
                  className="w-full bg-[#07243C] text-white py-3 px-6 rounded-lg font-medium hover:bg-[#0a2d47] transition-colors duration-200"
                >
                  Send Message
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-[#07243C] text-white py-16">
        <div className="max-w-6xl mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center mb-6">
                <Image
                  src="/images/wlc-logo.png"
                  alt="WLC Academy"
                  width={32}
                  height={32}
                  className="rounded-lg"
                />
                <span className="ml-3 text-lg font-medium">WLC Academy</span>
              </div>
              <p className="text-gray-400 leading-relaxed mb-8 max-w-md">
                Shaping minds, building futures. Where excellence meets innovation
                in education.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="w-10 h-10 bg-[#F3CC5C] rounded-lg flex items-center justify-center hover:bg-[#e6b84d] transition-colors duration-200">
                  <svg className="w-4 h-4 text-[#07243C]" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </a>
                <a href="#" className="w-10 h-10 bg-[#F3CC5C] rounded-lg flex items-center justify-center hover:bg-[#e6b84d] transition-colors duration-200">
                  <svg className="w-4 h-4 text-[#07243C]" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
                <a href="#" className="w-10 h-10 bg-[#F3CC5C] rounded-lg flex items-center justify-center hover:bg-[#e6b84d] transition-colors duration-200">
                  <svg className="w-4 h-4 text-[#07243C]" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                  </svg>
                </a>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-6">Quick Links</h3>
              <ul className="space-y-3">
                <li><a href="#about" className="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                <li><a href="#programs" className="text-gray-400 hover:text-white transition-colors">Programs</a></li>
                <li><a href="#contact" className="text-gray-400 hover:text-white transition-colors">Admissions</a></li>
                <li><a href="#contact" className="text-gray-400 hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-6">Resources</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Student Portal</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Parent Hub</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Academic Calendar</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">News & Events</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              &copy; 2024 WLC Academy. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">Privacy Policy</a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">Terms of Service</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
